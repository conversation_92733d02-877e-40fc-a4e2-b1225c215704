'use client'
import Image from 'next/image';
import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register ScrollTrigger plugin
gsap.registerPlugin(ScrollTrigger);

// Updated features data to match the screenshot
const features = [
  {
    id: 1,
    title: 'Get access to multiple lenders',
    description: 'Relying on a single bank can be limiting. We connect you to multiple institutional lenders giving you faster access to funds.',
    iconSrc: '/home/<USER>',
  },
  {
    id: 2,
    title: 'Apply in minutes, not weeks',
    description: 'Traditional financing involves complex forms and wait times. Madad simplifies it with a faster and easier digital application.',
    iconSrc: '/home/<USER>',
  },
  {
    id: 3,
    title: 'Build your Business Credit Score',
    description: 'Madad builds your business credit profile, helping you secure better loan terms and limits, with financial independence.',
    iconSrc: '/home/<USER>',
  },
  {
    id: 4,
    title: 'Become a part of Qatari SME ecosystem',
    description: "Join Qatar's largest SME community in making, to learn, share and grow with industry experts and fellow business owners.",
    iconSrc: '/home/<USER>',
  },
];

export const FeaturesSection = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const cardsRef = useRef<HTMLDivElement[]>([]);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Create a timeline for the animations
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 80%", // Start when the top of the section is 80% down the viewport
          end: "bottom 20%",
          toggleActions: "play none none reverse", // Play on enter, reverse on leave
        }
      });

      // First animate the header
      tl.fromTo(headerRef.current,
        {
          opacity: 0,
          y: 40
        },
        {
          opacity: 1,
          y: 0,
          duration: 1, // Increased from 0.8 to 1
          ease: "power2.out"
        }
      );

      // Then animate the cards with stagger
      tl.fromTo(cardsRef.current,
        {
          opacity: 0,
          y: 60, // Increased from 50 to 60
          scale: 0.9 // Reduced from 0.95 to 0.9 for more noticeable effect
        },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.8, // Increased from 0.6 to 0.8
          ease: "power2.out",
          stagger: 0.3 // Increased from 0.2 to 0.3 seconds delay between each card
        },
        "-=0.4" // Start 0.4 seconds before the header animation ends
      );
    }, sectionRef);

    return () => ctx.revert(); // Cleanup
  }, []);

  return (
    // Use a specific light gray background to match the wireframe
    <section ref={sectionRef} className="bg-[#F3F9F9] py-16">
      <div className="container mx-auto px-4 lg:px-8">
        {/* Section Header */}
        <div ref={headerRef} className="text-center mb-12 lg:mb-16">
          <h3 className="text-sm font-bold text-primary tracking-wider uppercase mb-3">
            Why Choose Us
          </h3>
          <h2 className="text-3xl md:text-4xl font-extrabold text-gray-900">
            Transform the way you finance your business
          </h2>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 lg:gap-8 max-w-8xl mx-auto">
          {features.map((feature, index) => (
            <div
              key={feature.id}
              ref={(el) => {
                if (el) cardsRef.current[index] = el;
              }}
              // Use a subtle border and shadow on the card
              className="rounded-lg px-6 py-2 flex items-start space-x-5"
            >
              {/* Icon with a custom light green background */}
              <div className="flex-shrink-0 w-16 h-16 bg-[#F0FAF7] rounded-lg flex items-center justify-center">
                <Image
                  src={feature.iconSrc}
                  alt={`${feature.title} icon`}
                  width={28 * 10}
                  height={28 * 10}
                />
              </div>

              {/* Text Content */}
              <div>
                <h3 className="text-2xl font-bold text-gray-800">
                  {feature.title}
                </h3>
                {/* Lighter text color for the description */}
                <p className="text-[#48484A] text-base leading-relaxed">
                  {feature.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
