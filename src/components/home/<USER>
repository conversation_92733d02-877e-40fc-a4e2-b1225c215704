'use client';

import Image from 'next/image';
import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register ScrollTrigger plugin
gsap.registerPlugin(ScrollTrigger);

// The new set of steps from the screenshot
const processSteps = [
  {
    id: 1,
    description: 'SME raises invoice to the Buyers against the delivered goods and/or services',
    color: "#161C2D"
  },
  {
    id: 2,
    description: "SME apply to the lender for discounting the invoice raised to it's buyers",
    color:"#359B7E"
  },
  {
    id: 3,
    description: 'Buyer authenticated the invoice for the lender to further process the application',
    color: '#87D283'
  },
  {
    id: 4,
    description: 'Post evaluation and acceptance the lender disburses the funds against the invoice to SME',
    color: "#78A974"
  },
  {
    id: 5,
    description: 'Buyer pays back the invoice amount on or before the due date, as per the invoice terms',
    color: "#006928"
  },
  {
    id: 6,
    description: '<PERSON><PERSON> pays the difference amount to the SME, post closure by the buyers',
    color: "#1EC4BE"
  },
];

export const ProcessSection = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const stepsRef = useRef<HTMLDivElement[]>([]);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Set initial state for all steps (dimmed except first one)
      gsap.set(stepsRef.current, {
        opacity: 0.3,
        scale: 0.95
      });

      // Highlight the first step initially
      if (stepsRef.current[0]) {
        gsap.set(stepsRef.current[0], {
          opacity: 1,
          scale: 1
        });
        gsap.set(stepsRef.current[0].querySelector('.step-circle'), {
          boxShadow: "0 0 20px rgba(93, 174, 98, 0.4)"
        });
      }

      // Create a pinned scroll trigger for the entire section
      ScrollTrigger.create({
        trigger: sectionRef.current,
        start: "top top",
        end: () => `+=${window.innerHeight * (processSteps.length - 1)}`, // Pin for multiple viewport heights
        pin: true,
        scrub: 1,
        onUpdate: (self) => {
          // Calculate which step should be active based on scroll progress
          const progress = self.progress;
          const totalSteps = processSteps.length;
          const currentStepIndex = Math.floor(progress * totalSteps);
          const clampedIndex = Math.min(currentStepIndex, totalSteps - 1);

          // Reset all steps to dimmed state
          stepsRef.current.forEach((step, index) => {
            if (step) {
              if (index === clampedIndex) {
                // Highlight current step
                gsap.to(step, {
                  opacity: 1,
                  scale: 1,
                  duration: 0.3,
                  ease: "power2.out"
                });
                gsap.to(step.querySelector('.step-circle'), {
                  boxShadow: "0 0 20px rgba(93, 174, 98, 0.4)",
                  duration: 0.3,
                  ease: "power2.out"
                });
              } else {
                // Dim other steps
                gsap.to(step, {
                  opacity: 0.3,
                  scale: 0.95,
                  duration: 0.3,
                  ease: "power2.out"
                });
                gsap.to(step.querySelector('.step-circle'), {
                  boxShadow: "0 0 0px rgba(93, 174, 98, 0)",
                  duration: 0.3,
                  ease: "power2.out"
                });
              }
            }
          });
        }
      });
    }, sectionRef);

    return () => ctx.revert(); // Cleanup
  }, []);

  return (
    <section ref={sectionRef} className="bg-white py-16">
      <div className="container mx-auto px-4 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12 lg:mb-16">
          <h3 className="text-sm font-bold text-[#5DAE62] tracking-wider uppercase mb-3">
            How It Works
          </h3>
          <h2 className="text-3xl md:text-4xl font-extrabold text-gray-900">
            Invoice discounting process
          </h2>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">

          {/* Left Column: Process Diagram */}
          <div className="flex justify-center">
            <Image
              src="/home/<USER>"
              alt="A diagram showing the invoice discounting process flow between a buyer, SME, and lending partners."
              width={550}
              height={550}
              priority
            />
          </div>

          {/* Right Column: Steps List */}
          <div className="space-y-5">
            {processSteps.map((step, index) => (
              <div
                key={step.id}
                ref={(el) => {
                  if (el) stepsRef.current[index] = el;
                }}
                className="flex items-start space-x-4 transition-all duration-300"
              >
                {/* Numbered Circle */}
                <div
                  className={`step-circle flex-shrink-0 w-8 h-8 text-white rounded-full flex items-center justify-center font-bold transition-all duration-300`}
                  style={{ backgroundColor: step.color }}
                >
                  {step.id}
                </div>
                {/* Description */}
                <p className="text-gray-900 transition-all duration-300">
                  {step.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};
